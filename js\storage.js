// Storage Management
class StorageManager {
    constructor() {
        this.storagePrefix = 'findmystuff_';
        this.init();
    }

    init() {
        this.setupStorageListeners();
        this.migrateOldData();
    }

    // Core storage methods
    set(key, value) {
        try {
            const fullKey = this.storagePrefix + key;
            const serializedValue = JSON.stringify(value);
            localStorage.setItem(fullKey, serializedValue);
            return true;
        } catch (error) {
            console.error('Error saving to localStorage:', error);
            return false;
        }
    }

    get(key, defaultValue = null) {
        try {
            const fullKey = this.storagePrefix + key;
            const item = localStorage.getItem(fullKey);
            return item ? JSON.parse(item) : defaultValue;
        } catch (error) {
            console.error('Error reading from localStorage:', error);
            return defaultValue;
        }
    }

    remove(key) {
        try {
            const fullKey = this.storagePrefix + key;
            localStorage.removeItem(fullKey);
            return true;
        } catch (error) {
            console.error('Error removing from localStorage:', error);
            return false;
        }
    }

    clear() {
        try {
            const keys = Object.keys(localStorage);
            keys.forEach(key => {
                if (key.startsWith(this.storagePrefix)) {
                    localStorage.removeItem(key);
                }
            });
            return true;
        } catch (error) {
            console.error('Error clearing localStorage:', error);
            return false;
        }
    }

    // Data-specific methods
    saveItems(items) {
        return this.set('items', items);
    }

    getItems() {
        return this.get('items', []);
    }

    saveUsers(users) {
        return this.set('users', users);
    }

    getUsers() {
        return this.get('users', []);
    }

    saveCurrentUser(user) {
        return this.set('current_user', user);
    }

    getCurrentUser() {
        return this.get('current_user', null);
    }

    saveSettings(settings) {
        return this.set('settings', settings);
    }

    getSettings() {
        return this.get('settings', this.getDefaultSettings());
    }

    saveSearchHistory(history) {
        return this.set('search_history', history);
    }

    getSearchHistory() {
        return this.get('search_history', []);
    }

    saveFavorites(favorites) {
        return this.set('favorites', favorites);
    }

    getFavorites() {
        return this.get('favorites', []);
    }

    saveNotifications(notifications) {
        return this.set('notifications', notifications);
    }

    getNotifications() {
        return this.get('notifications', []);
    }

    // User preferences
    saveUserPreferences(preferences) {
        return this.set('user_preferences', preferences);
    }

    getUserPreferences() {
        return this.get('user_preferences', this.getDefaultPreferences());
    }

    // Analytics data
    saveAnalytics(analytics) {
        return this.set('analytics', analytics);
    }

    getAnalytics() {
        return this.get('analytics', {});
    }

    // Cache management
    setCache(key, value, ttl = 3600000) { // Default 1 hour
        const cacheItem = {
            value: value,
            timestamp: Date.now(),
            ttl: ttl
        };
        return this.set(`cache_${key}`, cacheItem);
    }

    getCache(key) {
        const cacheItem = this.get(`cache_${key}`);
        if (!cacheItem) return null;

        const now = Date.now();
        if (now - cacheItem.timestamp > cacheItem.ttl) {
            this.remove(`cache_${key}`);
            return null;
        }

        return cacheItem.value;
    }

    clearCache() {
        const keys = Object.keys(localStorage);
        keys.forEach(key => {
            if (key.startsWith(this.storagePrefix + 'cache_')) {
                localStorage.removeItem(key);
            }
        });
    }

    // Data migration
    migrateOldData() {
        const version = this.get('version', '1.0.0');
        const currentVersion = '1.1.0';

        if (version !== currentVersion) {
            this.migrateData(version, currentVersion);
            this.set('version', currentVersion);
        }
    }

    migrateData(oldVersion, newVersion) {
        console.log(`Migrating data from ${oldVersion} to ${newVersion}`);

        // Migration logic here
        if (oldVersion === '1.0.0') {
            // Example migration
            const oldItems = this.get('items', []);
            const migratedItems = oldItems.map(item => ({
                ...item,
                createdAt: item.createdAt || Date.now(),
                views: item.views || 0
            }));
            this.saveItems(migratedItems);
        }
    }

    // Data validation
    validateData(data, schema) {
        // Simple validation - in a real app, you'd use a proper validation library
        if (!data || typeof data !== 'object') return false;
        
        for (const [key, type] of Object.entries(schema)) {
            if (data[key] === undefined) continue;
            if (typeof data[key] !== type) return false;
        }
        
        return true;
    }

    // Data backup and restore
    exportData() {
        const data = {
            items: this.getItems(),
            users: this.getUsers(),
            settings: this.getSettings(),
            searchHistory: this.getSearchHistory(),
            favorites: this.getFavorites(),
            notifications: this.getNotifications(),
            userPreferences: this.getUserPreferences(),
            analytics: this.getAnalytics(),
            exportDate: new Date().toISOString(),
            version: this.get('version', '1.0.0')
        };

        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = `findmystuff_backup_${Date.now()}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        return true;
    }

    importData(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (e) => {
                try {
                    const data = JSON.parse(e.target.result);
                    
                    // Validate data structure
                    if (!this.validateBackupData(data)) {
                        reject(new Error('Invalid backup file format'));
                        return;
                    }

                    // Import data
                    if (data.items) this.saveItems(data.items);
                    if (data.users) this.saveUsers(data.users);
                    if (data.settings) this.saveSettings(data.settings);
                    if (data.searchHistory) this.saveSearchHistory(data.searchHistory);
                    if (data.favorites) this.saveFavorites(data.favorites);
                    if (data.notifications) this.saveNotifications(data.notifications);
                    if (data.userPreferences) this.saveUserPreferences(data.userPreferences);
                    if (data.analytics) this.saveAnalytics(data.analytics);

                    resolve(true);
                } catch (error) {
                    reject(error);
                }
            };
            reader.onerror = () => reject(new Error('Failed to read file'));
            reader.readAsText(file);
        });
    }

    validateBackupData(data) {
        const requiredFields = ['items', 'users', 'settings', 'exportDate', 'version'];
        return requiredFields.every(field => data.hasOwnProperty(field));
    }

    // Storage event listeners
    setupStorageListeners() {
        window.addEventListener('storage', (e) => {
            if (e.key && e.key.startsWith(this.storagePrefix)) {
                this.handleStorageChange(e.key, e.oldValue, e.newValue);
            }
        });
    }

    handleStorageChange(key, oldValue, newValue) {
        const dataKey = key.replace(this.storagePrefix, '');
        
        // Handle different types of storage changes
        switch (dataKey) {
            case 'items':
                this.handleItemsChange(oldValue, newValue);
                break;
            case 'users':
                this.handleUsersChange(oldValue, newValue);
                break;
            case 'current_user':
                this.handleUserChange(oldValue, newValue);
                break;
            case 'settings':
                this.handleSettingsChange(oldValue, newValue);
                break;
        }
    }

    handleItemsChange(oldValue, newValue) {
        // Refresh items in the app
        if (app) {
            app.items = this.getItems();
            app.loadRecentListings();
            app.loadSuccessStories();
            app.updateStatistics();
        }
    }

    handleUsersChange(oldValue, newValue) {
        // Refresh users in the app
        if (app) {
            app.users = this.getUsers();
        }
    }

    handleUserChange(oldValue, newValue) {
        // Handle user login/logout
        if (app) {
            app.currentUser = this.getCurrentUser();
            app.updateAuthUI();
        }
    }

    handleSettingsChange(oldValue, newValue) {
        // Apply new settings
        const settings = this.getSettings();
        this.applySettings(settings);
    }

    // Settings management
    getDefaultSettings() {
        return {
            theme: 'light',
            language: 'en',
            notifications: {
                email: true,
                push: true,
                sound: true
            },
            privacy: {
                shareLocation: true,
                showContactInfo: true,
                allowMessages: true
            },
            display: {
                itemsPerPage: 12,
                showImages: true,
                compactMode: false
            }
        };
    }

    getDefaultPreferences() {
        return {
            categories: [],
            locations: [],
            savedSearches: [],
            favoriteItems: [],
            notificationSettings: {
                newItems: true,
                matches: true,
                updates: true
            }
        };
    }

    applySettings(settings) {
        // Apply theme
        if (settings.theme) {
            document.documentElement.setAttribute('data-theme', settings.theme);
        }

        // Apply language
        if (settings.language) {
            document.documentElement.setAttribute('lang', settings.language);
        }

        // Apply display settings
        if (settings.display) {
            // Update items per page
            if (searchManager && settings.display.itemsPerPage) {
                searchManager.itemsPerPage = settings.display.itemsPerPage;
            }
        }
    }

    // Data cleanup
    cleanupOldData() {
        const now = Date.now();
        const maxAge = 30 * 24 * 60 * 60 * 1000; // 30 days

        // Clean up old search history
        const searchHistory = this.getSearchHistory();
        const filteredHistory = searchHistory.filter(item => 
            now - item.timestamp < maxAge
        );
        this.saveSearchHistory(filteredHistory);

        // Clean up old notifications
        const notifications = this.getNotifications();
        const filteredNotifications = notifications.filter(notification => 
            now - notification.timestamp < maxAge
        );
        this.saveNotifications(filteredNotifications);

        // Clean up cache
        this.clearCache();
    }

    // Storage quota management
    getStorageUsage() {
        let totalSize = 0;
        const keys = Object.keys(localStorage);
        
        keys.forEach(key => {
            if (key.startsWith(this.storagePrefix)) {
                totalSize += localStorage.getItem(key).length;
            }
        });

        return {
            used: totalSize,
            available: 5 * 1024 * 1024, // 5MB typical limit
            percentage: (totalSize / (5 * 1024 * 1024)) * 100
        };
    }

    isStorageFull() {
        const usage = this.getStorageUsage();
        return usage.percentage > 90;
    }

    // Error handling
    handleStorageError(error) {
        console.error('Storage error:', error);
        
        // Try to recover
        if (error.name === 'QuotaExceededError') {
            this.cleanupOldData();
            ui.showNotification('Storage space cleared. Please try again.', 'warning');
        } else {
            ui.showNotification('Storage error occurred. Please refresh the page.', 'error');
        }
    }
}

// Initialize storage manager
const storageManager = new StorageManager();

// Global functions
window.storageManager = storageManager;
