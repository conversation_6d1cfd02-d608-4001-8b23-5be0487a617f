// FindMyStuff - Main Application
class FindMyStuffApp {
    constructor() {
        this.items = [];
        this.users = [];
        this.currentUser = null;
        this.currentView = 'home';
        this.searchResults = [];
        this.filters = {
            category: '',
            type: '',
            location: '',
            dateRange: ''
        };
        
        this.init();
    }

    init() {
        this.loadData();
        this.setupEventListeners();
        this.updateStatistics();
        this.loadRecentListings();
        this.loadSuccessStories();
        this.setupNavigation();
        this.animateStatistics();
    }

    // Data Management
    loadData() {
        // Load items from localStorage
        const savedItems = localStorage.getItem('findmystuff_items');
        this.items = savedItems ? JSON.parse(savedItems) : this.getSampleData();

        // Load users from localStorage
        const savedUsers = localStorage.getItem('findmystuff_users');
        this.users = savedUsers ? JSON.parse(savedUsers) : [];

        // Load current user
        const savedUser = localStorage.getItem('findmystuff_current_user');
        this.currentUser = savedUser ? JSON.parse(savedUser) : null;

        // Update UI based on user state
        this.updateAuthUI();
    }

    saveData() {
        localStorage.setItem('findmystuff_items', JSON.stringify(this.items));
        localStorage.setItem('findmystuff_users', JSON.stringify(this.users));
        if (this.currentUser) {
            localStorage.setItem('findmystuff_current_user', JSON.stringify(this.currentUser));
        }
    }

    // Sample data for demonstration
    getSampleData() {
        return [
            {
                id: '1',
                type: 'lost',
                title: 'iPhone 14 Pro - Black',
                category: 'electronics',
                description: 'Lost my iPhone 14 Pro at Central Park yesterday. It has a black case and a cracked screen protector. Last seen near the fountain.',
                location: 'Central Park, New York',
                coordinates: { lat: 40.7829, lng: -73.9654 },
                date: '2024-01-15',
                contactInfo: { email: '<EMAIL>', phone: '******-0123' },
                photos: [],
                status: 'active',
                reward: 100,
                createdAt: Date.now() - 86400000, // 1 day ago
                views: 45,
                userId: 'user1'
            },
            {
                id: '2',
                type: 'found',
                title: 'Gold Wedding Ring',
                category: 'jewelry',
                description: 'Found a beautiful gold wedding ring at the coffee shop on Main Street. It has an inscription inside.',
                location: 'Main Street Coffee Shop',
                coordinates: { lat: 40.7589, lng: -73.9851 },
                date: '2024-01-14',
                contactInfo: { email: '<EMAIL>', phone: '******-0456' },
                photos: [],
                status: 'active',
                reward: 0,
                createdAt: Date.now() - 172800000, // 2 days ago
                views: 23,
                userId: 'user2'
            },
            {
                id: '3',
                type: 'lost',
                title: 'Car Keys with Remote',
                category: 'keys',
                description: 'Lost my car keys with a black remote fob. Keys are for a Toyota Camry. Last seen at the grocery store parking lot.',
                location: 'Grocery Store Parking Lot',
                coordinates: { lat: 40.7505, lng: -73.9934 },
                date: '2024-01-13',
                contactInfo: { email: '<EMAIL>', phone: '******-0789' },
                photos: [],
                status: 'reunited',
                reward: 50,
                createdAt: Date.now() - 259200000, // 3 days ago
                views: 67,
                userId: 'user3'
            },
            {
                id: '4',
                type: 'found',
                title: 'Red Backpack',
                category: 'bags',
                description: 'Found a red backpack at the library. It contains books and a laptop. Please contact to identify.',
                location: 'Public Library',
                coordinates: { lat: 40.7527, lng: -73.9772 },
                date: '2024-01-12',
                contactInfo: { email: '<EMAIL>', phone: '******-0321' },
                photos: [],
                status: 'active',
                reward: 0,
                createdAt: Date.now() - 345600000, // 4 days ago
                views: 34,
                userId: 'user4'
            },
            {
                id: '5',
                type: 'lost',
                title: 'Silver Watch',
                category: 'jewelry',
                description: 'Lost my silver watch at the gym. It\'s a classic design with a leather strap.',
                location: 'Fitness Center',
                coordinates: { lat: 40.7614, lng: -73.9776 },
                date: '2024-01-11',
                contactInfo: { email: '<EMAIL>', phone: '******-0654' },
                photos: [],
                status: 'active',
                reward: 75,
                createdAt: Date.now() - 432000000, // 5 days ago
                views: 28,
                userId: 'user5'
            }
        ];
    }

    // Event Listeners
    setupEventListeners() {
        // Hero search
        const heroSearch = document.getElementById('heroSearch');
        const searchBtn = document.querySelector('.search-btn');
        
        if (heroSearch) {
            heroSearch.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.performSearch();
                }
            });
        }
        
        if (searchBtn) {
            searchBtn.addEventListener('click', () => {
                this.performSearch();
            });
        }

        // Quick action buttons
        const reportLostBtn = document.getElementById('reportLostBtn');
        const reportFoundBtn = document.getElementById('reportFoundBtn');
        
        if (reportLostBtn) {
            reportLostBtn.addEventListener('click', () => {
                this.showPostForm('lost');
            });
        }
        
        if (reportFoundBtn) {
            reportFoundBtn.addEventListener('click', () => {
                this.showPostForm('found');
            });
        }

        // Category and type filters
        const categoryFilter = document.getElementById('categoryFilter');
        const typeFilter = document.getElementById('typeFilter');
        
        if (categoryFilter) {
            categoryFilter.addEventListener('change', () => {
                this.filters.category = categoryFilter.value;
                this.performSearch();
            });
        }
        
        if (typeFilter) {
            typeFilter.addEventListener('change', () => {
                this.filters.type = typeFilter.value;
                this.performSearch();
            });
        }

        // Window resize for responsive navigation
        window.addEventListener('resize', () => {
            this.handleResize();
        });

        // Scroll events
        window.addEventListener('scroll', () => {
            this.handleScroll();
        });
    }

    // Navigation
    setupNavigation() {
        const navToggle = document.querySelector('.nav-toggle');
        const navMenu = document.querySelector('.nav-menu');
        const navLinks = document.querySelectorAll('.nav-link');

        if (navToggle) {
            navToggle.addEventListener('click', () => {
                navMenu.classList.toggle('active');
                navToggle.classList.toggle('active');
            });
        }

        navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const target = link.getAttribute('href').substring(1);
                this.navigateTo(target);
                
                // Close mobile menu
                navMenu.classList.remove('active');
                navToggle.classList.remove('active');
            });
        });
    }

    navigateTo(view) {
        this.currentView = view;
        
        // Update active nav link
        document.querySelectorAll('.nav-link').forEach(link => {
            link.classList.remove('active');
        });
        document.querySelector(`[href="#${view}"]`)?.classList.add('active');

        // Handle different views
        switch (view) {
            case 'home':
                this.showHome();
                break;
            case 'search':
                this.showSearch();
                break;
            case 'post':
                this.showPostForm();
                break;
            case 'about':
                this.showAbout();
                break;
            case 'contact':
                this.showContact();
                break;
        }
    }

    // Search functionality
    performSearch() {
        const searchTerm = document.getElementById('heroSearch')?.value || '';
        
        this.searchResults = this.items.filter(item => {
            const matchesSearch = !searchTerm || 
                item.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                item.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                item.location.toLowerCase().includes(searchTerm.toLowerCase());
            
            const matchesCategory = !this.filters.category || item.category === this.filters.category;
            const matchesType = !this.filters.type || item.type === this.filters.type;
            
            return matchesSearch && matchesCategory && matchesType;
        });

        this.displaySearchResults();
    }

    displaySearchResults() {
        const container = document.getElementById('recentListings');
        if (!container) return;

        if (this.searchResults.length === 0) {
            container.innerHTML = this.getEmptyStateHTML('No items found matching your search criteria.');
            return;
        }

        const itemsHTML = this.searchResults
            .slice(0, 6)
            .map(item => this.getItemCardHTML(item))
            .join('');

        container.innerHTML = itemsHTML;
    }

    // Item management
    addItem(itemData) {
        const newItem = {
            id: this.generateId(),
            ...itemData,
            createdAt: Date.now(),
            views: 0,
            status: 'active',
            userId: this.currentUser?.id || 'anonymous'
        };

        this.items.unshift(newItem);
        this.saveData();
        this.updateStatistics();
        this.loadRecentListings();

        return newItem;
    }

    updateItem(itemId, updates) {
        const index = this.items.findIndex(item => item.id === itemId);
        if (index !== -1) {
            this.items[index] = { ...this.items[index], ...updates };
            this.saveData();
            this.updateStatistics();
            this.loadRecentListings();
        }
    }

    deleteItem(itemId) {
        this.items = this.items.filter(item => item.id !== itemId);
        this.saveData();
        this.updateStatistics();
        this.loadRecentListings();
    }

    // UI Updates
    updateStatistics() {
        const totalItems = this.items.length;
        const reunitedItems = this.items.filter(item => item.status === 'reunited').length;
        const activeUsers = this.users.length;
        const successRate = totalItems > 0 ? Math.round((reunitedItems / totalItems) * 100) : 0;

        // Animate counters
        this.animateCounter('totalItems', totalItems);
        this.animateCounter('reunitedItems', reunitedItems);
        this.animateCounter('activeUsers', activeUsers);
        this.animateCounter('successRate', successRate, '%');
    }

    animateCounter(elementId, targetValue, suffix = '') {
        const element = document.getElementById(elementId);
        if (!element) return;

        const startValue = 0;
        const duration = 2000;
        const startTime = Date.now();

        const animate = () => {
            const elapsed = Date.now() - startTime;
            const progress = Math.min(elapsed / duration, 1);
            
            const currentValue = Math.floor(startValue + (targetValue - startValue) * progress);
            element.textContent = currentValue + suffix;

            if (progress < 1) {
                requestAnimationFrame(animate);
            }
        };

        animate();
    }

    loadRecentListings() {
        const container = document.getElementById('recentListings');
        if (!container) return;

        const recentItems = this.items
            .filter(item => item.status === 'active')
            .slice(0, 6);

        if (recentItems.length === 0) {
            container.innerHTML = this.getEmptyStateHTML('No recent listings available.');
            return;
        }

        const itemsHTML = recentItems.map(item => this.getItemCardHTML(item)).join('');
        container.innerHTML = itemsHTML;
    }

    loadSuccessStories() {
        const container = document.getElementById('successStories');
        if (!container) return;

        const successItems = this.items
            .filter(item => item.status === 'reunited')
            .slice(0, 3);

        if (successItems.length === 0) {
            container.innerHTML = this.getEmptyStateHTML('No success stories yet. Be the first!');
            return;
        }

        const storiesHTML = successItems.map(item => this.getSuccessStoryHTML(item)).join('');
        container.innerHTML = storiesHTML;
    }

    // HTML Generators
    getItemCardHTML(item) {
        const categoryIcons = {
            electronics: 'fas fa-mobile-alt',
            jewelry: 'fas fa-gem',
            documents: 'fas fa-file-alt',
            clothing: 'fas fa-tshirt',
            keys: 'fas fa-key',
            bags: 'fas fa-briefcase',
            other: 'fas fa-question'
        };

        const statusColors = {
            lost: 'error',
            found: 'success',
            reunited: 'warning'
        };

        return `
            <div class="item-card" data-id="${item.id}">
                <div class="item-card-image">
                    ${item.photos.length > 0 
                        ? `<img src="${item.photos[0]}" alt="${item.title}">`
                        : `<div class="item-card-image-placeholder">
                            <i class="${categoryIcons[item.category] || categoryIcons.other}"></i>
                           </div>`
                    }
                </div>
                <div class="item-card-content">
                    <div class="item-card-header">
                        <h3 class="item-card-title">${item.title}</h3>
                        <span class="item-card-status ${item.type}">${item.type}</span>
                    </div>
                    <span class="item-card-category">${item.category}</span>
                    <p class="item-card-description">${item.description}</p>
                    <div class="item-card-meta">
                        <span class="item-card-location">
                            <i class="fas fa-map-marker-alt"></i>
                            ${item.location}
                        </span>
                        <span class="item-card-date">
                            <i class="fas fa-calendar"></i>
                            ${this.formatDate(item.date)}
                        </span>
                    </div>
                    <div class="item-card-actions">
                        <button class="btn btn-outline" onclick="app.contactUser('${item.id}')">
                            <i class="fas fa-envelope"></i>
                            Contact
                        </button>
                        <button class="btn btn-primary" onclick="app.viewItem('${item.id}')">
                            <i class="fas fa-eye"></i>
                            View Details
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    getSuccessStoryHTML(item) {
        return `
            <div class="success-story-card">
                <div class="success-story-header">
                    <div class="success-story-avatar">
                        <i class="fas fa-heart"></i>
                    </div>
                    <div class="success-story-info">
                        <h4>Successfully Reunited!</h4>
                        <p>${this.formatDate(item.date)}</p>
                    </div>
                </div>
                <div class="success-story-content">
                    <p>"We're so grateful to have found our ${item.title} through FindMyStuff. The community really came through for us!"</p>
                </div>
                <div class="success-story-item">
                    <div class="success-story-item-image">
                        <i class="fas fa-${this.getCategoryIcon(item.category)}"></i>
                    </div>
                    <div class="success-story-item-info">
                        <h5>${item.title}</h5>
                        <p>${item.location}</p>
                    </div>
                </div>
            </div>
        `;
    }

    getEmptyStateHTML(message) {
        return `
            <div class="empty-state">
                <div class="empty-state-icon">
                    <i class="fas fa-search"></i>
                </div>
                <h3>No Items Found</h3>
                <p>${message}</p>
                <button class="btn btn-primary" onclick="app.showPostForm()">
                    <i class="fas fa-plus"></i>
                    Post Your First Item
                </button>
            </div>
        `;
    }

    // Utility functions
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    formatDate(dateString) {
        const date = new Date(dateString);
        const now = new Date();
        const diffTime = Math.abs(now - date);
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

        if (diffDays === 1) return 'Yesterday';
        if (diffDays < 7) return `${diffDays} days ago`;
        return date.toLocaleDateString();
    }

    getCategoryIcon(category) {
        const icons = {
            electronics: 'mobile-alt',
            jewelry: 'gem',
            documents: 'file-alt',
            clothing: 'tshirt',
            keys: 'key',
            bags: 'briefcase',
            other: 'question'
        };
        return icons[category] || 'question';
    }

    // Event handlers
    handleResize() {
        const navMenu = document.querySelector('.nav-menu');
        const navToggle = document.querySelector('.nav-toggle');
        
        if (window.innerWidth > 768) {
            navMenu.classList.remove('active');
            navToggle.classList.remove('active');
        }
    }

    handleScroll() {
        const navbar = document.querySelector('.navbar');
        if (window.scrollY > 100) {
            navbar.classList.add('scrolled');
        } else {
            navbar.classList.remove('scrolled');
        }
    }

    // Placeholder methods for future implementation
    showHome() {
        // Already implemented in init()
    }

    showSearch() {
        // Will be implemented in search.js
    }

    showPostForm(type = null) {
        // Will be implemented in forms.js
    }

    showAbout() {
        // Will be implemented in pages.js
    }

    showContact() {
        // Will be implemented in pages.js
    }

    contactUser(itemId) {
        // Will be implemented in contact.js
    }

    viewItem(itemId) {
        // Will be implemented in item-detail.js
    }

    updateAuthUI() {
        const loginBtn = document.getElementById('loginBtn');
        const registerBtn = document.getElementById('registerBtn');
        const navAuth = document.querySelector('.nav-auth');

        if (this.currentUser) {
            navAuth.innerHTML = `
                <span class="user-name">Hello, ${this.currentUser.name}</span>
                <button class="btn btn-outline" onclick="app.logout()">Logout</button>
            `;
        } else {
            navAuth.innerHTML = `
                <button class="btn btn-outline" id="loginBtn">Login</button>
                <button class="btn btn-primary" id="registerBtn">Register</button>
            `;
        }
    }

    logout() {
        this.currentUser = null;
        localStorage.removeItem('findmystuff_current_user');
        this.updateAuthUI();
        this.showNotification('Logged out successfully', 'success');
    }

    showNotification(message, type = 'info') {
        // Will be implemented in ui.js
    }
}

// Initialize the application
const app = new FindMyStuffApp();

// Global functions for event handlers
window.app = app;
