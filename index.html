<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FindMyStuff - Lost & Found Platform</title>
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/components.css">
    <link rel="stylesheet" href="styles/responsive.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <i class="fas fa-search-location"></i>
                <span>FindMyStuff</span>
            </div>
            <div class="nav-menu">
                <a href="#home" class="nav-link active">Home</a>
                <a href="#search" class="nav-link">Search</a>
                <a href="#post" class="nav-link">Post Item</a>
                <a href="#about" class="nav-link">About</a>
                <a href="#contact" class="nav-link">Contact</a>
            </div>
            <div class="nav-auth">
                <button class="btn btn-outline" id="loginBtn">Login</button>
                <button class="btn btn-primary" id="registerBtn">Register</button>
            </div>
            <div class="nav-toggle">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main>
        <!-- Hero Section -->
        <section id="home" class="hero">
            <div class="hero-container">
                <div class="hero-content">
                    <h1 class="hero-title">
                        Find What You've Lost, 
                        <span class="gradient-text">Return What You've Found</span>
                    </h1>
                    <p class="hero-subtitle">
                        Connect with your community to reunite lost items with their owners. 
                        Fast, secure, and reliable lost & found platform.
                    </p>
                    
                    <!-- Search Bar -->
                    <div class="search-container">
                        <div class="search-box">
                            <i class="fas fa-search"></i>
                            <input type="text" id="heroSearch" placeholder="Search for lost or found items...">
                            <button class="search-btn">Search</button>
                        </div>
                        <div class="search-filters">
                            <select id="categoryFilter">
                                <option value="">All Categories</option>
                                <option value="electronics">Electronics</option>
                                <option value="jewelry">Jewelry</option>
                                <option value="documents">Documents</option>
                                <option value="clothing">Clothing</option>
                                <option value="keys">Keys</option>
                                <option value="bags">Bags</option>
                                <option value="other">Other</option>
                            </select>
                            <select id="typeFilter">
                                <option value="">All Types</option>
                                <option value="lost">Lost</option>
                                <option value="found">Found</option>
                            </select>
                        </div>
                    </div>

                    <!-- Quick Action Buttons -->
                    <div class="hero-actions">
                        <button class="btn btn-primary btn-large" id="reportLostBtn">
                            <i class="fas fa-plus"></i>
                            Report Lost Item
                        </button>
                        <button class="btn btn-secondary btn-large" id="reportFoundBtn">
                            <i class="fas fa-hand-holding-heart"></i>
                            Report Found Item
                        </button>
                    </div>
                </div>
                <div class="hero-image">
                    <div class="hero-graphic">
                        <i class="fas fa-search-location"></i>
                    </div>
                </div>
            </div>
        </section>

        <!-- Statistics Section -->
        <section class="stats">
            <div class="container">
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-box"></i>
                        </div>
                        <div class="stat-content">
                            <h3 class="stat-number" id="totalItems">0</h3>
                            <p class="stat-label">Items Posted</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-heart"></i>
                        </div>
                        <div class="stat-content">
                            <h3 class="stat-number" id="reunitedItems">0</h3>
                            <p class="stat-label">Successfully Reunited</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-content">
                            <h3 class="stat-number" id="activeUsers">0</h3>
                            <p class="stat-label">Active Users</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-percentage"></i>
                        </div>
                        <div class="stat-content">
                            <h3 class="stat-number" id="successRate">0%</h3>
                            <p class="stat-label">Success Rate</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Recent Listings Section -->
        <section class="recent-listings">
            <div class="container">
                <div class="section-header">
                    <h2>Recent Listings</h2>
                    <a href="#search" class="view-all">View All Items</a>
                </div>
                <div class="listings-grid" id="recentListings">
                    <!-- Items will be loaded dynamically -->
                </div>
            </div>
        </section>

        <!-- Success Stories Section -->
        <section class="success-stories">
            <div class="container">
                <div class="section-header">
                    <h2>Success Stories</h2>
                    <p>Real stories of reunited items and happy owners</p>
                </div>
                <div class="stories-grid" id="successStories">
                    <!-- Success stories will be loaded dynamically -->
                </div>
            </div>
        </section>

        <!-- How It Works Section -->
        <section class="how-it-works">
            <div class="container">
                <div class="section-header">
                    <h2>How It Works</h2>
                    <p>Simple steps to find or return lost items</p>
                </div>
                <div class="steps-grid">
                    <div class="step-card">
                        <div class="step-number">1</div>
                        <div class="step-icon">
                            <i class="fas fa-edit"></i>
                        </div>
                        <h3>Post Your Item</h3>
                        <p>Report a lost item or post a found item with details and photos</p>
                    </div>
                    <div class="step-card">
                        <div class="step-number">2</div>
                        <div class="step-icon">
                            <i class="fas fa-search"></i>
                        </div>
                        <h3>Search & Match</h3>
                        <p>Use our powerful search to find matches or browse recent listings</p>
                    </div>
                    <div class="step-card">
                        <div class="step-number">3</div>
                        <div class="step-icon">
                            <i class="fas fa-comments"></i>
                        </div>
                        <h3>Connect & Verify</h3>
                        <p>Contact the other party and verify ownership details</p>
                    </div>
                    <div class="step-card">
                        <div class="step-number">4</div>
                        <div class="step-icon">
                            <i class="fas fa-heart"></i>
                        </div>
                        <h3>Reunite & Celebrate</h3>
                        <p>Successfully reunite items with their owners</p>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <div class="footer-logo">
                        <i class="fas fa-search-location"></i>
                        <span>FindMyStuff</span>
                    </div>
                    <p>Connecting communities through lost and found items. Making reunions possible, one item at a time.</p>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-facebook"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                        <a href="#"><i class="fab fa-linkedin"></i></a>
                    </div>
                </div>
                <div class="footer-section">
                    <h3>Quick Links</h3>
                    <ul>
                        <li><a href="#home">Home</a></li>
                        <li><a href="#search">Search Items</a></li>
                        <li><a href="#post">Post Item</a></li>
                        <li><a href="#about">About Us</a></li>
                        <li><a href="#contact">Contact</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h3>Categories</h3>
                    <ul>
                        <li><a href="#search?category=electronics">Electronics</a></li>
                        <li><a href="#search?category=jewelry">Jewelry</a></li>
                        <li><a href="#search?category=documents">Documents</a></li>
                        <li><a href="#search?category=clothing">Clothing</a></li>
                        <li><a href="#search?category=keys">Keys</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h3>Support</h3>
                    <ul>
                        <li><a href="#faq">FAQ</a></li>
                        <li><a href="#guidelines">Guidelines</a></li>
                        <li><a href="#safety">Safety Tips</a></li>
                        <li><a href="#privacy">Privacy Policy</a></li>
                        <li><a href="#terms">Terms of Service</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 FindMyStuff. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Modals -->
    <div id="loginModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>Login</h2>
            <form id="loginForm">
                <div class="form-group">
                    <label for="loginEmail">Email</label>
                    <input type="email" id="loginEmail" required>
                </div>
                <div class="form-group">
                    <label for="loginPassword">Password</label>
                    <input type="password" id="loginPassword" required>
                </div>
                <button type="submit" class="btn btn-primary">Login</button>
            </form>
            <p>Don't have an account? <a href="#" id="showRegister">Register here</a></p>
        </div>
    </div>

    <div id="registerModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>Register</h2>
            <form id="registerForm">
                <div class="form-group">
                    <label for="registerName">Full Name</label>
                    <input type="text" id="registerName" required>
                </div>
                <div class="form-group">
                    <label for="registerEmail">Email</label>
                    <input type="email" id="registerEmail" required>
                </div>
                <div class="form-group">
                    <label for="registerPassword">Password</label>
                    <input type="password" id="registerPassword" required>
                </div>
                <div class="form-group">
                    <label for="registerPhone">Phone (optional)</label>
                    <input type="tel" id="registerPhone">
                </div>
                <button type="submit" class="btn btn-primary">Register</button>
            </form>
            <p>Already have an account? <a href="#" id="showLogin">Login here</a></p>
        </div>
    </div>

    <!-- Loading Spinner -->
    <div id="loadingSpinner" class="loading-spinner">
        <div class="spinner"></div>
    </div>

    <!-- Scripts -->
    <script src="js/app.js"></script>
    <script src="js/modals.js"></script>
    <script src="js/search.js"></script>
    <script src="js/forms.js"></script>
    <script src="js/storage.js"></script>
    <script src="js/ui.js"></script>
</body>
</html>
