/* Item Cards */
.item-card {
    background: var(--white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    overflow: hidden;
    transition: all var(--transition-normal);
    border: 1px solid var(--gray-200);
    position: relative;
}

.item-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.item-card-image {
    width: 100%;
    height: 200px;
    background: var(--gray-100);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.item-card-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-normal);
}

.item-card:hover .item-card-image img {
    transform: scale(1.05);
}

.item-card-image-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--gradient-primary);
    color: var(--white);
    font-size: var(--font-size-2xl);
}

.item-card-content {
    padding: var(--spacing-6);
}

.item-card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-3);
}

.item-card-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--gray-900);
    margin: 0;
    line-height: 1.3;
}

.item-card-status {
    padding: var(--spacing-1) var(--spacing-3);
    border-radius: var(--radius-lg);
    font-size: var(--font-size-xs);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.item-card-status.lost {
    background: var(--error-color);
    color: var(--white);
}

.item-card-status.found {
    background: var(--success-color);
    color: var(--white);
}

.item-card-status.reunited {
    background: var(--warning-color);
    color: var(--white);
}

.item-card-category {
    display: inline-block;
    padding: var(--spacing-1) var(--spacing-3);
    background: var(--gray-100);
    color: var(--gray-700);
    border-radius: var(--radius-lg);
    font-size: var(--font-size-xs);
    font-weight: 500;
    margin-bottom: var(--spacing-3);
}

.item-card-description {
    color: var(--gray-600);
    font-size: var(--font-size-sm);
    line-height: 1.5;
    margin-bottom: var(--spacing-4);
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.item-card-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: var(--font-size-xs);
    color: var(--gray-500);
    margin-bottom: var(--spacing-4);
}

.item-card-location {
    display: flex;
    align-items: center;
    gap: var(--spacing-1);
}

.item-card-date {
    display: flex;
    align-items: center;
    gap: var(--spacing-1);
}

.item-card-actions {
    display: flex;
    gap: var(--spacing-2);
}

.item-card-actions .btn {
    flex: 1;
    padding: var(--spacing-2) var(--spacing-4);
    font-size: var(--font-size-xs);
}

/* Forms */
.form-container {
    max-width: 600px;
    margin: 0 auto;
    padding: var(--spacing-8);
    background: var(--white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
}

.form-header {
    text-align: center;
    margin-bottom: var(--spacing-8);
}

.form-header h2 {
    color: var(--gray-900);
    margin-bottom: var(--spacing-2);
}

.form-header p {
    color: var(--gray-600);
    font-size: var(--font-size-sm);
}

.form-group {
    margin-bottom: var(--spacing-6);
}

.form-group label {
    display: block;
    margin-bottom: var(--spacing-2);
    font-weight: 500;
    color: var(--gray-700);
    font-size: var(--font-size-sm);
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: var(--spacing-3) var(--spacing-4);
    border: 2px solid var(--gray-200);
    border-radius: var(--radius-lg);
    font-size: var(--font-size-base);
    transition: all var(--transition-normal);
    background: var(--white);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-4);
}

.form-actions {
    display: flex;
    gap: var(--spacing-4);
    justify-content: flex-end;
    margin-top: var(--spacing-8);
}

.form-actions .btn {
    min-width: 120px;
}

/* File Upload */
.file-upload {
    position: relative;
    display: inline-block;
    width: 100%;
}

.file-upload input[type="file"] {
    position: absolute;
    left: -9999px;
}

.file-upload-label {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-8);
    border: 2px dashed var(--gray-300);
    border-radius: var(--radius-lg);
    background: var(--gray-50);
    cursor: pointer;
    transition: all var(--transition-normal);
}

.file-upload-label:hover {
    border-color: var(--primary-color);
    background: var(--gray-100);
}

.file-upload-icon {
    font-size: var(--font-size-3xl);
    color: var(--gray-400);
    margin-bottom: var(--spacing-3);
}

.file-upload-text {
    text-align: center;
    color: var(--gray-600);
}

.file-upload-text strong {
    color: var(--primary-color);
}

.file-preview {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: var(--spacing-3);
    margin-top: var(--spacing-4);
}

.file-preview-item {
    position: relative;
    aspect-ratio: 1;
    border-radius: var(--radius-lg);
    overflow: hidden;
    background: var(--gray-100);
}

.file-preview-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.file-preview-remove {
    position: absolute;
    top: var(--spacing-1);
    right: var(--spacing-1);
    width: 24px;
    height: 24px;
    background: rgba(0, 0, 0, 0.5);
    color: var(--white);
    border: none;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: var(--font-size-xs);
}

/* Modals */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
}

.modal.show {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background: var(--white);
    border-radius: var(--radius-xl);
    padding: var(--spacing-8);
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    position: relative;
    transform: scale(0.9);
    transition: transform var(--transition-normal);
}

.modal.show .modal-content {
    transform: scale(1);
}

.modal .close {
    position: absolute;
    top: var(--spacing-4);
    right: var(--spacing-4);
    background: none;
    border: none;
    font-size: var(--font-size-2xl);
    color: var(--gray-400);
    cursor: pointer;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all var(--transition-normal);
}

.modal .close:hover {
    background: var(--gray-100);
    color: var(--gray-600);
}

/* Search Results */
.search-results {
    padding: var(--spacing-8) 0;
}

.search-filters-panel {
    background: var(--white);
    padding: var(--spacing-6);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    margin-bottom: var(--spacing-8);
}

.search-filters-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-4);
}

.search-results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-6);
}

.search-results-count {
    color: var(--gray-600);
    font-size: var(--font-size-sm);
}

.search-results-sort {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
}

.search-results-sort select {
    padding: var(--spacing-2) var(--spacing-4);
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-lg);
    background: var(--white);
    font-size: var(--font-size-sm);
}

.view-toggle {
    display: flex;
    gap: var(--spacing-2);
}

.view-toggle button {
    padding: var(--spacing-2) var(--spacing-3);
    border: 1px solid var(--gray-300);
    background: var(--white);
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: all var(--transition-normal);
}

.view-toggle button.active {
    background: var(--primary-color);
    color: var(--white);
    border-color: var(--primary-color);
}

/* Success Stories */
.success-story-card {
    background: var(--white);
    border-radius: var(--radius-xl);
    padding: var(--spacing-6);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
    transition: all var(--transition-normal);
}

.success-story-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-lg);
}

.success-story-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-4);
    margin-bottom: var(--spacing-4);
}

.success-story-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: var(--gradient-success);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: var(--font-size-xl);
    font-weight: 600;
}

.success-story-info h4 {
    color: var(--gray-900);
    margin-bottom: var(--spacing-1);
}

.success-story-info p {
    color: var(--gray-600);
    font-size: var(--font-size-sm);
    margin: 0;
}

.success-story-content {
    color: var(--gray-700);
    line-height: 1.6;
    margin-bottom: var(--spacing-4);
}

.success-story-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    padding: var(--spacing-3);
    background: var(--gray-50);
    border-radius: var(--radius-lg);
    margin-bottom: var(--spacing-3);
}

.success-story-item-image {
    width: 50px;
    height: 50px;
    border-radius: var(--radius-lg);
    background: var(--gray-200);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-lg);
    color: var(--gray-500);
}

.success-story-item-info h5 {
    color: var(--gray-900);
    margin-bottom: var(--spacing-1);
    font-size: var(--font-size-sm);
}

.success-story-item-info p {
    color: var(--gray-600);
    font-size: var(--font-size-xs);
    margin: 0;
}

/* Empty States */
.empty-state {
    text-align: center;
    padding: var(--spacing-16) var(--spacing-4);
}

.empty-state-icon {
    width: 120px;
    height: 120px;
    background: var(--gray-100);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-6);
    font-size: var(--font-size-4xl);
    color: var(--gray-400);
}

.empty-state h3 {
    color: var(--gray-900);
    margin-bottom: var(--spacing-3);
}

.empty-state p {
    color: var(--gray-600);
    max-width: 400px;
    margin: 0 auto var(--spacing-6);
}

/* Notifications */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: var(--spacing-4) var(--spacing-6);
    border-radius: var(--radius-lg);
    color: var(--white);
    font-weight: 500;
    z-index: 10001;
    transform: translateX(100%);
    transition: transform var(--transition-normal);
    max-width: 400px;
}

.notification.show {
    transform: translateX(0);
}

.notification.success {
    background: var(--success-color);
}

.notification.error {
    background: var(--error-color);
}

.notification.warning {
    background: var(--warning-color);
}

.notification.info {
    background: var(--primary-color);
}

/* Loading States */
.skeleton {
    background: linear-gradient(90deg, var(--gray-200) 25%, var(--gray-100) 50%, var(--gray-200) 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
    border-radius: var(--radius-md);
}

@keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

.skeleton-text {
    height: 1em;
    margin-bottom: var(--spacing-2);
}

.skeleton-text:last-child {
    width: 60%;
}

.skeleton-image {
    width: 100%;
    height: 200px;
}

.skeleton-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
}

/* Map Integration */
.map-container {
    width: 100%;
    height: 300px;
    border-radius: var(--radius-lg);
    overflow: hidden;
    background: var(--gray-100);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--gray-500);
    font-size: var(--font-size-lg);
}

.map-placeholder {
    text-align: center;
}

.map-placeholder i {
    font-size: var(--font-size-4xl);
    margin-bottom: var(--spacing-3);
    opacity: 0.5;
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: var(--spacing-2);
    margin-top: var(--spacing-8);
}

.pagination button {
    padding: var(--spacing-2) var(--spacing-4);
    border: 1px solid var(--gray-300);
    background: var(--white);
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: all var(--transition-normal);
    min-width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.pagination button:hover {
    background: var(--gray-50);
    border-color: var(--gray-400);
}

.pagination button.active {
    background: var(--primary-color);
    color: var(--white);
    border-color: var(--primary-color);
}

.pagination button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Tooltips */
.tooltip {
    position: relative;
    display: inline-block;
}

.tooltip .tooltip-text {
    visibility: hidden;
    width: 200px;
    background-color: var(--gray-900);
    color: var(--white);
    text-align: center;
    border-radius: var(--radius-lg);
    padding: var(--spacing-2) var(--spacing-3);
    position: absolute;
    z-index: 1;
    bottom: 125%;
    left: 50%;
    margin-left: -100px;
    opacity: 0;
    transition: opacity var(--transition-normal);
    font-size: var(--font-size-xs);
}

.tooltip .tooltip-text::after {
    content: "";
    position: absolute;
    top: 100%;
    left: 50%;
    margin-left: -5px;
    border-width: 5px;
    border-style: solid;
    border-color: var(--gray-900) transparent transparent transparent;
}

.tooltip:hover .tooltip-text {
    visibility: visible;
    opacity: 1;
}

/* Accessibility */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Focus styles */
*:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    :root {
        --primary-color: #0000ff;
        --gray-900: #000000;
        --gray-800: #1a1a1a;
        --gray-700: #333333;
        --gray-600: #4d4d4d;
        --gray-500: #666666;
        --gray-400: #808080;
        --gray-300: #999999;
        --gray-200: #b3b3b3;
        --gray-100: #cccccc;
        --gray-50: #e6e6e6;
        --white: #ffffff;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}
