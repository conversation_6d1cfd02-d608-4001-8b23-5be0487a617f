// Modal Management
class ModalManager {
    constructor() {
        this.activeModal = null;
        this.init();
    }

    init() {
        this.setupModalListeners();
        this.setupAuthModals();
    }

    setupModalListeners() {
        // Close modal when clicking outside
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal')) {
                this.closeModal(e.target);
            }
        });

        // Close modal with escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.activeModal) {
                this.closeModal(this.activeModal);
            }
        });

        // Close buttons
        document.querySelectorAll('.modal .close').forEach(closeBtn => {
            closeBtn.addEventListener('click', (e) => {
                const modal = e.target.closest('.modal');
                this.closeModal(modal);
            });
        });
    }

    setupAuthModals() {
        // Login modal
        const loginBtn = document.getElementById('loginBtn');
        const loginModal = document.getElementById('loginModal');
        const loginForm = document.getElementById('loginForm');

        if (loginBtn) {
            loginBtn.addEventListener('click', () => {
                this.openModal(loginModal);
            });
        }

        if (loginForm) {
            loginForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleLogin();
            });
        }

        // Register modal
        const registerBtn = document.getElementById('registerBtn');
        const registerModal = document.getElementById('registerModal');
        const registerForm = document.getElementById('registerForm');

        if (registerBtn) {
            registerBtn.addEventListener('click', () => {
                this.openModal(registerModal);
            });
        }

        if (registerForm) {
            registerForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleRegister();
            });
        }

        // Switch between login and register
        const showRegister = document.getElementById('showRegister');
        const showLogin = document.getElementById('showLogin');

        if (showRegister) {
            showRegister.addEventListener('click', (e) => {
                e.preventDefault();
                this.closeModal(loginModal);
                this.openModal(registerModal);
            });
        }

        if (showLogin) {
            showLogin.addEventListener('click', (e) => {
                e.preventDefault();
                this.closeModal(registerModal);
                this.openModal(loginModal);
            });
        }
    }

    openModal(modal) {
        if (!modal) return;

        // Close any active modal
        if (this.activeModal) {
            this.closeModal(this.activeModal);
        }

        // Show modal
        modal.classList.add('show');
        this.activeModal = modal;

        // Focus first input
        const firstInput = modal.querySelector('input');
        if (firstInput) {
            firstInput.focus();
        }

        // Prevent body scroll
        document.body.style.overflow = 'hidden';
    }

    closeModal(modal) {
        if (!modal) return;

        modal.classList.remove('show');
        this.activeModal = null;

        // Restore body scroll
        document.body.style.overflow = '';

        // Clear form
        const form = modal.querySelector('form');
        if (form) {
            form.reset();
        }
    }

    handleLogin() {
        const email = document.getElementById('loginEmail').value;
        const password = document.getElementById('loginPassword').value;

        if (!email || !password) {
            this.showFormError('Please fill in all fields');
            return;
        }

        // Simple validation
        if (!this.isValidEmail(email)) {
            this.showFormError('Please enter a valid email address');
            return;
        }

        // Simulate login process
        this.showLoading('Logging in...');

        setTimeout(() => {
            // Check if user exists
            const user = app.users.find(u => u.email === email && u.password === password);
            
            if (user) {
                app.currentUser = user;
                app.saveData();
                app.updateAuthUI();
                this.closeModal(document.getElementById('loginModal'));
                this.showSuccess('Login successful!');
            } else {
                this.showFormError('Invalid email or password');
            }
            
            this.hideLoading();
        }, 1000);
    }

    handleRegister() {
        const name = document.getElementById('registerName').value;
        const email = document.getElementById('registerEmail').value;
        const password = document.getElementById('registerPassword').value;
        const phone = document.getElementById('registerPhone').value;

        if (!name || !email || !password) {
            this.showFormError('Please fill in all required fields');
            return;
        }

        // Validation
        if (!this.isValidEmail(email)) {
            this.showFormError('Please enter a valid email address');
            return;
        }

        if (password.length < 6) {
            this.showFormError('Password must be at least 6 characters long');
            return;
        }

        // Check if user already exists
        if (app.users.find(u => u.email === email)) {
            this.showFormError('User with this email already exists');
            return;
        }

        // Simulate registration process
        this.showLoading('Creating account...');

        setTimeout(() => {
            const newUser = {
                id: app.generateId(),
                name,
                email,
                password,
                phone,
                createdAt: Date.now()
            };

            app.users.push(newUser);
            app.currentUser = newUser;
            app.saveData();
            app.updateAuthUI();

            this.closeModal(document.getElementById('registerModal'));
            this.showSuccess('Account created successfully!');
            this.hideLoading();
        }, 1000);
    }

    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    showFormError(message) {
        const errorDiv = document.createElement('div');
        errorDiv.className = 'form-error';
        errorDiv.textContent = message;
        errorDiv.style.cssText = `
            color: var(--error-color);
            font-size: var(--font-size-sm);
            margin-top: var(--spacing-2);
            padding: var(--spacing-2);
            background: rgba(239, 68, 68, 0.1);
            border-radius: var(--radius-md);
            border: 1px solid var(--error-color);
        `;

        const form = this.activeModal.querySelector('form');
        const existingError = form.querySelector('.form-error');
        
        if (existingError) {
            existingError.remove();
        }

        form.appendChild(errorDiv);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (errorDiv.parentNode) {
                errorDiv.remove();
            }
        }, 5000);
    }

    showSuccess(message) {
        // Create notification
        const notification = document.createElement('div');
        notification.className = 'notification success show';
        notification.textContent = message;
        document.body.appendChild(notification);

        // Remove after 3 seconds
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 300);
        }, 3000);
    }

    showLoading(message) {
        const loadingDiv = document.createElement('div');
        loadingDiv.className = 'modal-loading';
        loadingDiv.innerHTML = `
            <div class="spinner"></div>
            <p>${message}</p>
        `;
        loadingDiv.style.cssText = `
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.9);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            z-index: 10;
            border-radius: var(--radius-xl);
        `;

        this.activeModal.appendChild(loadingDiv);
    }

    hideLoading() {
        const loadingDiv = this.activeModal.querySelector('.modal-loading');
        if (loadingDiv) {
            loadingDiv.remove();
        }
    }

    // Generic modal methods
    showConfirmModal(title, message, onConfirm, onCancel) {
        const modal = document.createElement('div');
        modal.className = 'modal';
        modal.innerHTML = `
            <div class="modal-content">
                <h2>${title}</h2>
                <p>${message}</p>
                <div class="form-actions">
                    <button class="btn btn-outline" id="cancelBtn">Cancel</button>
                    <button class="btn btn-primary" id="confirmBtn">Confirm</button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
        this.openModal(modal);

        // Event listeners
        modal.querySelector('#confirmBtn').addEventListener('click', () => {
            this.closeModal(modal);
            if (onConfirm) onConfirm();
        });

        modal.querySelector('#cancelBtn').addEventListener('click', () => {
            this.closeModal(modal);
            if (onCancel) onCancel();
        });
    }

    showInfoModal(title, message) {
        const modal = document.createElement('div');
        modal.className = 'modal';
        modal.innerHTML = `
            <div class="modal-content">
                <h2>${title}</h2>
                <p>${message}</p>
                <div class="form-actions">
                    <button class="btn btn-primary" id="okBtn">OK</button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
        this.openModal(modal);

        modal.querySelector('#okBtn').addEventListener('click', () => {
            this.closeModal(modal);
        });
    }
}

// Initialize modal manager
const modalManager = new ModalManager();
