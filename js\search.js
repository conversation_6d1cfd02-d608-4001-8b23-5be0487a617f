// Search Management
class SearchManager {
    constructor() {
        this.searchResults = [];
        this.currentFilters = {
            query: '',
            category: '',
            type: '',
            location: '',
            dateRange: '',
            status: 'active'
        };
        this.currentPage = 1;
        this.itemsPerPage = 12;
        this.init();
    }

    init() {
        this.setupSearchListeners();
        this.setupFilters();
    }

    setupSearchListeners() {
        // Global search input
        const searchInputs = document.querySelectorAll('input[type="text"][placeholder*="search"], input[type="text"][placeholder*="Search"]');
        searchInputs.forEach(input => {
            input.addEventListener('input', ui.debounce((e) => {
                this.currentFilters.query = e.target.value;
                this.performSearch();
            }, 300));
        });

        // Search button
        const searchButtons = document.querySelectorAll('.search-btn');
        searchButtons.forEach(btn => {
            btn.addEventListener('click', () => {
                this.performSearch();
            });
        });
    }

    setupFilters() {
        // Category filter
        const categoryFilters = document.querySelectorAll('select[id*="category"], select[name*="category"]');
        categoryFilters.forEach(filter => {
            filter.addEventListener('change', (e) => {
                this.currentFilters.category = e.target.value;
                this.performSearch();
            });
        });

        // Type filter
        const typeFilters = document.querySelectorAll('select[id*="type"], select[name*="type"]');
        typeFilters.forEach(filter => {
            filter.addEventListener('change', (e) => {
                this.currentFilters.type = e.target.value;
                this.performSearch();
            });
        });

        // Location filter
        const locationInputs = document.querySelectorAll('input[placeholder*="location"], input[name*="location"]');
        locationInputs.forEach(input => {
            input.addEventListener('input', ui.debounce((e) => {
                this.currentFilters.location = e.target.value;
                this.performSearch();
            }, 300));
        });

        // Date range filter
        const dateInputs = document.querySelectorAll('input[type="date"]');
        dateInputs.forEach(input => {
            input.addEventListener('change', (e) => {
                this.currentFilters.dateRange = e.target.value;
                this.performSearch();
            });
        });
    }

    performSearch() {
        this.currentPage = 1;
        this.searchResults = this.filterItems();
        this.displaySearchResults();
        this.updateSearchStats();
    }

    filterItems() {
        let filteredItems = [...app.items];

        // Query filter
        if (this.currentFilters.query) {
            const query = this.currentFilters.query.toLowerCase();
            filteredItems = filteredItems.filter(item => 
                item.title.toLowerCase().includes(query) ||
                item.description.toLowerCase().includes(query) ||
                item.location.toLowerCase().includes(query) ||
                item.category.toLowerCase().includes(query)
            );
        }

        // Category filter
        if (this.currentFilters.category) {
            filteredItems = filteredItems.filter(item => 
                item.category === this.currentFilters.category
            );
        }

        // Type filter
        if (this.currentFilters.type) {
            filteredItems = filteredItems.filter(item => 
                item.type === this.currentFilters.type
            );
        }

        // Location filter
        if (this.currentFilters.location) {
            const location = this.currentFilters.location.toLowerCase();
            filteredItems = filteredItems.filter(item => 
                item.location.toLowerCase().includes(location)
            );
        }

        // Date range filter
        if (this.currentFilters.dateRange) {
            const filterDate = new Date(this.currentFilters.dateRange);
            filteredItems = filteredItems.filter(item => {
                const itemDate = new Date(item.date);
                return itemDate.toDateString() === filterDate.toDateString();
            });
        }

        // Status filter
        if (this.currentFilters.status) {
            filteredItems = filteredItems.filter(item => 
                item.status === this.currentFilters.status
            );
        }

        // Sort by date (newest first)
        filteredItems.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

        return filteredItems;
    }

    displaySearchResults() {
        const container = document.getElementById('searchResults') || document.getElementById('recentListings');
        if (!container) return;

        const startIndex = (this.currentPage - 1) * this.itemsPerPage;
        const endIndex = startIndex + this.itemsPerPage;
        const pageItems = this.searchResults.slice(startIndex, endIndex);

        if (pageItems.length === 0) {
            container.innerHTML = this.getEmptySearchHTML();
            return;
        }

        const itemsHTML = pageItems.map(item => app.getItemCardHTML(item)).join('');
        container.innerHTML = itemsHTML;

        // Add pagination if needed
        if (this.searchResults.length > this.itemsPerPage) {
            this.addPagination(container);
        }
    }

    getEmptySearchHTML() {
        const hasFilters = Object.values(this.currentFilters).some(filter => filter !== '' && filter !== 'active');
        
        if (hasFilters) {
            return `
                <div class="empty-state">
                    <div class="empty-state-icon">
                        <i class="fas fa-search"></i>
                    </div>
                    <h3>No Items Found</h3>
                    <p>No items match your current search criteria. Try adjusting your filters or search terms.</p>
                    <div class="empty-state-actions">
                        <button class="btn btn-outline" onclick="searchManager.clearFilters()">
                            <i class="fas fa-times"></i>
                            Clear Filters
                        </button>
                        <button class="btn btn-primary" onclick="formManager.showPostForm()">
                            <i class="fas fa-plus"></i>
                            Post New Item
                        </button>
                    </div>
                </div>
            `;
        } else {
            return `
                <div class="empty-state">
                    <div class="empty-state-icon">
                        <i class="fas fa-box-open"></i>
                    </div>
                    <h3>No Items Available</h3>
                    <p>There are currently no items posted. Be the first to post a lost or found item!</p>
                    <button class="btn btn-primary" onclick="formManager.showPostForm()">
                        <i class="fas fa-plus"></i>
                        Post Your First Item
                    </button>
                </div>
            `;
        }
    }

    addPagination(container) {
        const totalPages = Math.ceil(this.searchResults.length / this.itemsPerPage);
        
        const paginationHTML = `
            <div class="pagination">
                <button class="pagination-btn" onclick="searchManager.goToPage(${this.currentPage - 1})" 
                        ${this.currentPage === 1 ? 'disabled' : ''}>
                    <i class="fas fa-chevron-left"></i>
                </button>
                
                ${this.getPaginationNumbers(totalPages)}
                
                <button class="pagination-btn" onclick="searchManager.goToPage(${this.currentPage + 1})"
                        ${this.currentPage === totalPages ? 'disabled' : ''}>
                    <i class="fas fa-chevron-right"></i>
                </button>
            </div>
        `;

        container.insertAdjacentHTML('afterend', paginationHTML);
    }

    getPaginationNumbers(totalPages) {
        let paginationHTML = '';
        const maxVisiblePages = 5;
        
        if (totalPages <= maxVisiblePages) {
            for (let i = 1; i <= totalPages; i++) {
                paginationHTML += `
                    <button class="pagination-btn ${i === this.currentPage ? 'active' : ''}" 
                            onclick="searchManager.goToPage(${i})">
                        ${i}
                    </button>
                `;
            }
        } else {
            // Show first page
            paginationHTML += `
                <button class="pagination-btn ${1 === this.currentPage ? 'active' : ''}" 
                        onclick="searchManager.goToPage(1)">
                    1
                </button>
            `;

            // Show ellipsis if needed
            if (this.currentPage > 3) {
                paginationHTML += '<span class="pagination-ellipsis">...</span>';
            }

            // Show current page and neighbors
            const start = Math.max(2, this.currentPage - 1);
            const end = Math.min(totalPages - 1, this.currentPage + 1);
            
            for (let i = start; i <= end; i++) {
                paginationHTML += `
                    <button class="pagination-btn ${i === this.currentPage ? 'active' : ''}" 
                            onclick="searchManager.goToPage(${i})">
                        ${i}
                    </button>
                `;
            }

            // Show ellipsis if needed
            if (this.currentPage < totalPages - 2) {
                paginationHTML += '<span class="pagination-ellipsis">...</span>';
            }

            // Show last page
            if (totalPages > 1) {
                paginationHTML += `
                    <button class="pagination-btn ${totalPages === this.currentPage ? 'active' : ''}" 
                            onclick="searchManager.goToPage(${totalPages})">
                        ${totalPages}
                    </button>
                `;
            }
        }

        return paginationHTML;
    }

    goToPage(page) {
        const totalPages = Math.ceil(this.searchResults.length / this.itemsPerPage);
        if (page < 1 || page > totalPages) return;

        this.currentPage = page;
        this.displaySearchResults();
        
        // Scroll to top of results
        const container = document.getElementById('searchResults') || document.getElementById('recentListings');
        if (container) {
            container.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }
    }

    updateSearchStats() {
        const statsContainer = document.getElementById('searchStats');
        if (!statsContainer) return;

        const totalResults = this.searchResults.length;
        const hasFilters = Object.values(this.currentFilters).some(filter => filter !== '' && filter !== 'active');

        statsContainer.innerHTML = `
            <div class="search-stats">
                <span class="results-count">${totalResults} item${totalResults !== 1 ? 's' : ''} found</span>
                ${hasFilters ? '<button class="btn btn-sm btn-outline" onclick="searchManager.clearFilters()">Clear Filters</button>' : ''}
            </div>
        `;
    }

    clearFilters() {
        this.currentFilters = {
            query: '',
            category: '',
            type: '',
            location: '',
            dateRange: '',
            status: 'active'
        };

        // Clear form inputs
        const searchInputs = document.querySelectorAll('input[type="text"][placeholder*="search"], input[type="text"][placeholder*="Search"]');
        searchInputs.forEach(input => input.value = '');

        const categoryFilters = document.querySelectorAll('select[id*="category"], select[name*="category"]');
        categoryFilters.forEach(filter => filter.value = '');

        const typeFilters = document.querySelectorAll('select[id*="type"], select[name*="type"]');
        typeFilters.forEach(filter => filter.value = '');

        const locationInputs = document.querySelectorAll('input[placeholder*="location"], input[name*="location"]');
        locationInputs.forEach(input => input.value = '');

        const dateInputs = document.querySelectorAll('input[type="date"]');
        dateInputs.forEach(input => input.value = '');

        this.performSearch();
        ui.showToast('Filters cleared', 'info');
    }

    // Advanced search methods
    searchByLocation(location) {
        this.currentFilters.location = location;
        this.performSearch();
    }

    searchByCategory(category) {
        this.currentFilters.category = category;
        this.performSearch();
    }

    searchByType(type) {
        this.currentFilters.type = type;
        this.performSearch();
    }

    searchByDateRange(startDate, endDate) {
        this.currentFilters.dateRange = { start: startDate, end: endDate };
        this.performSearch();
    }

    // Search suggestions
    getSearchSuggestions(query) {
        if (!query || query.length < 2) return [];

        const suggestions = new Set();
        const queryLower = query.toLowerCase();

        app.items.forEach(item => {
            // Title suggestions
            if (item.title.toLowerCase().includes(queryLower)) {
                suggestions.add(item.title);
            }

            // Category suggestions
            if (item.category.toLowerCase().includes(queryLower)) {
                suggestions.add(item.category);
            }

            // Location suggestions
            if (item.location.toLowerCase().includes(queryLower)) {
                suggestions.add(item.location);
            }
        });

        return Array.from(suggestions).slice(0, 5);
    }

    // Save search
    saveSearch() {
        const savedSearches = JSON.parse(localStorage.getItem('findmystuff_saved_searches') || '[]');
        const searchName = `Search ${savedSearches.length + 1}`;
        
        const savedSearch = {
            id: Date.now(),
            name: searchName,
            filters: { ...this.currentFilters },
            createdAt: Date.now()
        };

        savedSearches.push(savedSearch);
        localStorage.setItem('findmystuff_saved_searches', JSON.stringify(savedSearches));
        
        ui.showToast('Search saved successfully', 'success');
    }

    // Load saved search
    loadSavedSearch(searchId) {
        const savedSearches = JSON.parse(localStorage.getItem('findmystuff_saved_searches') || '[]');
        const savedSearch = savedSearches.find(search => search.id === searchId);
        
        if (savedSearch) {
            this.currentFilters = { ...savedSearch.filters };
            this.performSearch();
            ui.showToast('Search loaded successfully', 'success');
        }
    }

    // Export search results
    exportSearchResults() {
        const data = {
            searchDate: new Date().toISOString(),
            filters: this.currentFilters,
            results: this.searchResults,
            totalResults: this.searchResults.length
        };

        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = `search-results-${Date.now()}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        ui.showToast('Search results exported', 'success');
    }

    // Search analytics
    getSearchAnalytics() {
        const analytics = {
            totalSearches: 0,
            popularCategories: {},
            popularLocations: {},
            searchTrends: {}
        };

        // This would typically come from a backend
        // For now, we'll return mock data
        return analytics;
    }
}

// Initialize search manager
const searchManager = new SearchManager();

// Global functions
window.searchManager = searchManager;
