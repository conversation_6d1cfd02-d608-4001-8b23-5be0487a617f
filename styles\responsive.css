/* Responsive Design - Mobile First Approach */

/* Extra Small Devices (phones, 320px and up) */
@media (max-width: 575.98px) {
    .container {
        padding: 0 var(--spacing-3);
    }
    
    .hero-title {
        font-size: var(--font-size-3xl);
        line-height: 1.2;
    }
    
    .hero-subtitle {
        font-size: var(--font-size-base);
    }
    
    .hero-container {
        grid-template-columns: 1fr;
        gap: var(--spacing-8);
        text-align: center;
    }
    
    .hero-graphic {
        width: 200px;
        height: 200px;
    }
    
    .hero-graphic i {
        font-size: 80px;
    }
    
    .search-filters {
        flex-direction: column;
        gap: var(--spacing-2);
    }
    
    .hero-actions {
        flex-direction: column;
        gap: var(--spacing-3);
    }
    
    .hero-actions .btn {
        width: 100%;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-4);
    }
    
    .stat-card {
        padding: var(--spacing-6);
    }
    
    .listings-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-4);
    }
    
    .stories-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-4);
    }
    
    .steps-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-6);
    }
    
    .footer-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-6);
        text-align: center;
    }
    
    .nav-menu {
        position: fixed;
        top: 70px;
        left: -100%;
        width: 100%;
        height: calc(100vh - 70px);
        background: var(--white);
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        padding-top: var(--spacing-8);
        transition: left var(--transition-normal);
        box-shadow: var(--shadow-lg);
    }
    
    .nav-menu.active {
        left: 0;
    }
    
    .nav-menu .nav-link {
        font-size: var(--font-size-lg);
        padding: var(--spacing-4) 0;
        width: 100%;
        text-align: center;
        border-bottom: 1px solid var(--gray-200);
    }
    
    .nav-toggle {
        display: flex;
    }
    
    .nav-auth {
        display: none;
    }
    
    .modal-content {
        padding: var(--spacing-6);
        margin: var(--spacing-4);
        width: calc(100% - 2rem);
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .form-actions {
        flex-direction: column;
        gap: var(--spacing-3);
    }
    
    .form-actions .btn {
        width: 100%;
    }
    
    .search-results-header {
        flex-direction: column;
        gap: var(--spacing-4);
        align-items: stretch;
    }
    
    .search-filters-grid {
        grid-template-columns: 1fr;
    }
    
    .item-card-actions {
        flex-direction: column;
        gap: var(--spacing-2);
    }
    
    .item-card-actions .btn {
        width: 100%;
    }
    
    .success-story-header {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-3);
    }
    
    .notification {
        right: var(--spacing-3);
        left: var(--spacing-3);
        max-width: none;
    }
}

/* Small Devices (landscape phones, 576px and up) */
@media (min-width: 576px) and (max-width: 767.98px) {
    .hero-container {
        grid-template-columns: 1fr;
        gap: var(--spacing-8);
        text-align: center;
    }
    
    .hero-graphic {
        width: 250px;
        height: 250px;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .listings-grid {
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    }
    
    .stories-grid {
        grid-template-columns: 1fr;
    }
    
    .steps-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .footer-content {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .search-filters-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Medium Devices (tablets, 768px and up) */
@media (min-width: 768px) and (max-width: 991.98px) {
    .hero-container {
        grid-template-columns: 1fr 1fr;
        gap: var(--spacing-12);
    }
    
    .hero-title {
        font-size: var(--font-size-4xl);
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .listings-grid {
        grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    }
    
    .stories-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .steps-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .footer-content {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .search-filters-grid {
        grid-template-columns: repeat(3, 1fr);
    }
    
    .nav-menu {
        gap: var(--spacing-6);
    }
}

/* Large Devices (desktops, 992px and up) */
@media (min-width: 992px) and (max-width: 1199.98px) {
    .hero-container {
        gap: var(--spacing-16);
    }
    
    .stats-grid {
        grid-template-columns: repeat(4, 1fr);
    }
    
    .listings-grid {
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    }
    
    .stories-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .steps-grid {
        grid-template-columns: repeat(4, 1fr);
    }
    
    .footer-content {
        grid-template-columns: repeat(4, 1fr);
    }
    
    .search-filters-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}

/* Extra Large Devices (large desktops, 1200px and up) */
@media (min-width: 1200px) {
    .hero-container {
        gap: var(--spacing-20);
    }
    
    .hero-title {
        font-size: var(--font-size-5xl);
    }
    
    .stats-grid {
        grid-template-columns: repeat(4, 1fr);
    }
    
    .listings-grid {
        grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    }
    
    .stories-grid {
        grid-template-columns: repeat(3, 1fr);
    }
    
    .steps-grid {
        grid-template-columns: repeat(4, 1fr);
    }
    
    .footer-content {
        grid-template-columns: repeat(4, 1fr);
    }
    
    .search-filters-grid {
        grid-template-columns: repeat(5, 1fr);
    }
}

/* Landscape Orientation Adjustments */
@media (orientation: landscape) and (max-height: 600px) {
    .hero {
        padding: 80px 0 60px;
    }
    
    .hero-container {
        gap: var(--spacing-8);
    }
    
    .hero-graphic {
        width: 200px;
        height: 200px;
    }
    
    .hero-graphic i {
        font-size: 80px;
    }
    
    .modal-content {
        max-height: 80vh;
    }
}

/* High DPI Displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .hero-graphic {
        border-width: 1px;
    }
    
    .item-card {
        border-width: 0.5px;
    }
}

/* Print Styles */
@media print {
    .navbar,
    .footer,
    .modal,
    .notification,
    .loading-spinner {
        display: none !important;
    }
    
    .hero {
        background: none !important;
        color: black !important;
    }
    
    .item-card {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #ccc;
    }
    
    .btn {
        display: none !important;
    }
    
    body {
        font-size: 12pt;
        line-height: 1.4;
    }
    
    h1, h2, h3, h4, h5, h6 {
        page-break-after: avoid;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    :root {
        --white: #1a1a1a;
        --gray-50: #2a2a2a;
        --gray-100: #3a3a3a;
        --gray-200: #4a4a4a;
        --gray-300: #5a5a5a;
        --gray-400: #6a6a6a;
        --gray-500: #7a7a7a;
        --gray-600: #8a8a8a;
        --gray-700: #9a9a9a;
        --gray-800: #aaaaaa;
        --gray-900: #ffffff;
    }
    
    .navbar {
        background: rgba(26, 26, 26, 0.95);
        border-bottom-color: var(--gray-200);
    }
    
    .modal-content {
        background: var(--white);
        color: var(--gray-900);
    }
    
    .form-group input,
    .form-group select,
    .form-group textarea {
        background: var(--gray-100);
        border-color: var(--gray-300);
        color: var(--gray-900);
    }
}

/* Reduced Motion Preferences */
@media (prefers-reduced-motion: reduce) {
    .hero-graphic,
    .item-card,
    .stat-card,
    .success-story-card,
    .step-card {
        animation: none !important;
        transition: none !important;
    }
    
    .btn::before {
        display: none !important;
    }
    
    .skeleton {
        animation: none !important;
    }
}

/* Focus Visible for Keyboard Navigation */
@media (any-hover: none) {
    .btn:hover {
        transform: none;
    }
    
    .item-card:hover {
        transform: none;
    }
    
    .stat-card:hover {
        transform: none;
    }
}

/* Container Queries (Future Support) */
@supports (container-type: inline-size) {
    .item-card {
        container-type: inline-size;
    }
    
    @container (max-width: 300px) {
        .item-card-content {
            padding: var(--spacing-4);
        }
        
        .item-card-title {
            font-size: var(--font-size-base);
        }
    }
}

/* Touch Device Optimizations */
@media (hover: none) and (pointer: coarse) {
    .btn {
        min-height: 44px;
        min-width: 44px;
    }
    
    .nav-link {
        padding: var(--spacing-3) var(--spacing-4);
        min-height: 44px;
        display: flex;
        align-items: center;
    }
    
    .item-card-actions .btn {
        min-height: 44px;
    }
    
    .form-group input,
    .form-group select,
    .form-group textarea {
        min-height: 44px;
        font-size: 16px; /* Prevents zoom on iOS */
    }
}

/* Ultra-wide Screens */
@media (min-width: 1400px) {
    .container {
        max-width: 1320px;
    }
    
    .hero-container {
        max-width: 1320px;
    }
    
    .listings-grid {
        grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
    }
}

/* Foldable Device Support */
@media (screen-spanning: single-fold-vertical) {
    .hero-container {
        grid-template-columns: 1fr;
        text-align: center;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Accessibility Improvements */
@media (prefers-contrast: high) {
    .btn {
        border: 2px solid currentColor;
    }
    
    .item-card {
        border: 2px solid var(--gray-300);
    }
    
    .form-group input,
    .form-group select,
    .form-group textarea {
        border-width: 3px;
    }
}

/* Performance Optimizations */
@media (update: slow) {
    .hero-graphic {
        animation: none;
    }
    
    .skeleton {
        animation: none;
    }
}
