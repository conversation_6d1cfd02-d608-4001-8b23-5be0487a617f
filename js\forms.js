// Forms Management
class FormManager {
    constructor() {
        this.currentForm = null;
        this.fileUploads = new Map();
        this.init();
    }

    init() {
        this.setupFormValidation();
        this.setupFileUploads();
    }

    // Show post form
    showPostForm(type = null) {
        const modal = document.createElement('div');
        modal.className = 'modal';
        modal.innerHTML = this.getPostFormHTML(type);
        
        document.body.appendChild(modal);
        modalManager.openModal(modal);
        
        this.setupPostFormListeners(modal, type);
    }

    getPostFormHTML(type) {
        const formType = type || 'lost';
        const title = formType === 'lost' ? 'Report Lost Item' : 'Report Found Item';
        const description = formType === 'lost' 
            ? 'Help us find your lost item by providing detailed information.'
            : 'Help reunite a found item with its owner by providing details.';

        return `
            <div class="modal-content">
                <span class="close">&times;</span>
                <div class="form-header">
                    <h2>${title}</h2>
                    <p>${description}</p>
                </div>
                <form id="postItemForm" class="post-form">
                    <div class="form-group">
                        <label for="itemType">Item Type *</label>
                        <select id="itemType" required>
                            <option value="lost" ${formType === 'lost' ? 'selected' : ''}>Lost Item</option>
                            <option value="found" ${formType === 'found' ? 'selected' : ''}>Found Item</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="itemTitle">Item Title *</label>
                        <input type="text" id="itemTitle" placeholder="e.g., iPhone 14 Pro, Gold Wedding Ring" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="itemCategory">Category *</label>
                        <select id="itemCategory" required>
                            <option value="">Select Category</option>
                            <option value="electronics">Electronics</option>
                            <option value="jewelry">Jewelry</option>
                            <option value="documents">Documents</option>
                            <option value="clothing">Clothing</option>
                            <option value="keys">Keys</option>
                            <option value="bags">Bags</option>
                            <option value="other">Other</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="itemDescription">Description *</label>
                        <textarea id="itemDescription" placeholder="Provide detailed description of the item, including any unique features, colors, brands, etc." required></textarea>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="itemLocation">Location *</label>
                            <input type="text" id="itemLocation" placeholder="Where was it lost/found?" required>
                        </div>
                        <div class="form-group">
                            <label for="itemDate">Date *</label>
                            <input type="date" id="itemDate" required>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="itemReward">Reward Amount (Optional)</label>
                        <input type="number" id="itemReward" placeholder="0" min="0">
                    </div>
                    
                    <div class="form-group">
                        <label>Photos (Optional)</label>
                        <div class="file-upload">
                            <input type="file" id="itemPhotos" multiple accept="image/*">
                            <label for="itemPhotos" class="file-upload-label">
                                <div class="file-upload-icon">
                                    <i class="fas fa-cloud-upload-alt"></i>
                                </div>
                                <div class="file-upload-text">
                                    <strong>Click to upload</strong> or drag and drop<br>
                                    <small>PNG, JPG up to 5MB each</small>
                                </div>
                            </label>
                        </div>
                        <div class="file-preview" id="filePreview"></div>
                    </div>
                    
                    <div class="form-group">
                        <label for="contactEmail">Contact Email *</label>
                        <input type="email" id="contactEmail" placeholder="<EMAIL>" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="contactPhone">Contact Phone (Optional)</label>
                        <input type="tel" id="contactPhone" placeholder="+****************">
                    </div>
                    
                    <div class="form-actions">
                        <button type="button" class="btn btn-outline" onclick="modalManager.closeModal(this.closest('.modal'))">Cancel</button>
                        <button type="submit" class="btn btn-primary">Post Item</button>
                    </div>
                </form>
            </div>
        `;
    }

    setupPostFormListeners(modal, type) {
        const form = modal.querySelector('#postItemForm');
        const fileInput = modal.querySelector('#itemPhotos');
        const filePreview = modal.querySelector('#filePreview');

        // Set default date to today
        const dateInput = modal.querySelector('#itemDate');
        if (dateInput) {
            dateInput.value = new Date().toISOString().split('T')[0];
        }

        // Form submission
        form.addEventListener('submit', (e) => {
            e.preventDefault();
            this.handlePostFormSubmit(form);
        });

        // File upload handling
        if (fileInput) {
            fileInput.addEventListener('change', (e) => {
                this.handleFileUpload(e.target.files, filePreview);
            });

            // Drag and drop
            const uploadLabel = modal.querySelector('.file-upload-label');
            if (uploadLabel) {
                uploadLabel.addEventListener('dragover', (e) => {
                    e.preventDefault();
                    uploadLabel.style.borderColor = 'var(--primary-color)';
                    uploadLabel.style.background = 'var(--gray-100)';
                });

                uploadLabel.addEventListener('dragleave', (e) => {
                    e.preventDefault();
                    uploadLabel.style.borderColor = 'var(--gray-300)';
                    uploadLabel.style.background = 'var(--gray-50)';
                });

                uploadLabel.addEventListener('drop', (e) => {
                    e.preventDefault();
                    uploadLabel.style.borderColor = 'var(--gray-300)';
                    uploadLabel.style.background = 'var(--gray-50)';
                    
                    const files = e.dataTransfer.files;
                    this.handleFileUpload(files, filePreview);
                });
            }
        }

        // Real-time validation
        this.setupFormValidation(form);
    }

    handlePostFormSubmit(form) {
        const formData = this.getFormData(form);
        
        if (!this.validateFormData(formData)) {
            return;
        }

        // Show loading
        const submitBtn = form.querySelector('button[type="submit"]');
        const originalText = submitBtn.textContent;
        submitBtn.textContent = 'Posting...';
        submitBtn.disabled = true;

        // Simulate API call
        setTimeout(() => {
            try {
                const newItem = app.addItem(formData);
                
                // Close modal
                modalManager.closeModal(form.closest('.modal'));
                
                // Show success message
                ui.showNotification('Item posted successfully!', 'success');
                
                // Reset form
                form.reset();
                
            } catch (error) {
                ui.showNotification('Error posting item. Please try again.', 'error');
            } finally {
                submitBtn.textContent = originalText;
                submitBtn.disabled = false;
            }
        }, 1500);
    }

    getFormData(form) {
        const formData = new FormData(form);
        const data = {
            type: formData.get('itemType') || form.querySelector('#itemType').value,
            title: formData.get('itemTitle') || form.querySelector('#itemTitle').value,
            category: formData.get('itemCategory') || form.querySelector('#itemCategory').value,
            description: formData.get('itemDescription') || form.querySelector('#itemDescription').value,
            location: formData.get('itemLocation') || form.querySelector('#itemLocation').value,
            date: formData.get('itemDate') || form.querySelector('#itemDate').value,
            reward: parseInt(formData.get('itemReward') || form.querySelector('#itemReward').value) || 0,
            contactInfo: {
                email: formData.get('contactEmail') || form.querySelector('#contactEmail').value,
                phone: formData.get('contactPhone') || form.querySelector('#contactPhone').value
            },
            photos: this.fileUploads.get(form.id) || []
        };

        return data;
    }

    validateFormData(data) {
        const errors = [];

        if (!data.title || data.title.trim().length < 3) {
            errors.push('Item title must be at least 3 characters long');
        }

        if (!data.category) {
            errors.push('Please select a category');
        }

        if (!data.description || data.description.trim().length < 10) {
            errors.push('Description must be at least 10 characters long');
        }

        if (!data.location || data.location.trim().length < 3) {
            errors.push('Please provide a valid location');
        }

        if (!data.date) {
            errors.push('Please select a date');
        }

        if (!data.contactInfo.email || !ui.isValidEmail(data.contactInfo.email)) {
            errors.push('Please provide a valid email address');
        }

        if (data.contactInfo.phone && !ui.isValidPhone(data.contactInfo.phone)) {
            errors.push('Please provide a valid phone number');
        }

        if (errors.length > 0) {
            ui.showNotification(errors.join('\n'), 'error');
            return false;
        }

        return true;
    }

    handleFileUpload(files, previewContainer) {
        const maxFiles = 5;
        const maxSize = 5 * 1024 * 1024; // 5MB
        const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];

        if (files.length > maxFiles) {
            ui.showNotification(`Maximum ${maxFiles} files allowed`, 'error');
            return;
        }

        Array.from(files).forEach(file => {
            // Validate file type
            if (!allowedTypes.includes(file.type)) {
                ui.showNotification(`${file.name} is not a valid image file`, 'error');
                return;
            }

            // Validate file size
            if (file.size > maxSize) {
                ui.showNotification(`${file.name} is too large (max 5MB)`, 'error');
                return;
            }

            // Create preview
            const reader = new FileReader();
            reader.onload = (e) => {
                const previewItem = document.createElement('div');
                previewItem.className = 'file-preview-item';
                previewItem.innerHTML = `
                    <img src="${e.target.result}" alt="${file.name}">
                    <button class="file-preview-remove" onclick="formManager.removeFilePreview(this)">
                        <i class="fas fa-times"></i>
                    </button>
                `;
                previewContainer.appendChild(previewItem);

                // Store file data
                const formId = previewContainer.closest('form').id;
                if (!this.fileUploads.has(formId)) {
                    this.fileUploads.set(formId, []);
                }
                this.fileUploads.get(formId).push({
                    file: file,
                    preview: e.target.result
                });
            };
            reader.readAsDataURL(file);
        });
    }

    removeFilePreview(button) {
        const previewItem = button.closest('.file-preview-item');
        const formId = previewItem.closest('form').id;
        const index = Array.from(previewItem.parentNode.children).indexOf(previewItem);
        
        // Remove from storage
        if (this.fileUploads.has(formId)) {
            this.fileUploads.get(formId).splice(index, 1);
        }
        
        previewItem.remove();
    }

    setupFormValidation(form) {
        const inputs = form.querySelectorAll('input, select, textarea');
        
        inputs.forEach(input => {
            // Real-time validation
            input.addEventListener('blur', () => {
                this.validateField(input);
            });

            input.addEventListener('input', () => {
                this.clearFieldError(input);
            });
        });
    }

    validateField(field) {
        const value = field.value.trim();
        let isValid = true;
        let errorMessage = '';

        // Required field validation
        if (field.hasAttribute('required') && !value) {
            isValid = false;
            errorMessage = 'This field is required';
        }

        // Email validation
        if (field.type === 'email' && value && !ui.isValidEmail(value)) {
            isValid = false;
            errorMessage = 'Please enter a valid email address';
        }

        // Phone validation
        if (field.type === 'tel' && value && !ui.isValidPhone(value)) {
            isValid = false;
            errorMessage = 'Please enter a valid phone number';
        }

        // Min length validation
        if (field.hasAttribute('minlength')) {
            const minLength = parseInt(field.getAttribute('minlength'));
            if (value.length < minLength) {
                isValid = false;
                errorMessage = `Minimum ${minLength} characters required`;
            }
        }

        if (!isValid) {
            this.showFieldError(field, errorMessage);
        } else {
            this.clearFieldError(field);
        }

        return isValid;
    }

    showFieldError(field, message) {
        this.clearFieldError(field);
        
        field.classList.add('error');
        
        const errorDiv = document.createElement('div');
        errorDiv.className = 'field-error';
        errorDiv.textContent = message;
        errorDiv.style.cssText = `
            color: var(--error-color);
            font-size: var(--font-size-xs);
            margin-top: var(--spacing-1);
        `;
        
        field.parentNode.appendChild(errorDiv);
    }

    clearFieldError(field) {
        field.classList.remove('error');
        
        const errorDiv = field.parentNode.querySelector('.field-error');
        if (errorDiv) {
            errorDiv.remove();
        }
    }

    // Setup file uploads
    setupFileUploads() {
        // Global file upload handling
        document.addEventListener('change', (e) => {
            if (e.target.type === 'file') {
                const previewContainer = e.target.parentNode.querySelector('.file-preview');
                if (previewContainer) {
                    this.handleFileUpload(e.target.files, previewContainer);
                }
            }
        });
    }
}

// Initialize form manager
const formManager = new FormManager();

// Global functions
window.formManager = formManager;
